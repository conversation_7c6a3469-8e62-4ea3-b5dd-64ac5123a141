import type { InneihRecordFilterInput } from '~/gql/graphql'
import { SearchIcon, SlashIcon } from 'lucide-react'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '~/components/ui/breadcrumb'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { useAppForm } from '~/hooks/form'

interface Props {
  searchInneih: (data: InneihRecordFilterInput | null) => void
  isLoading: boolean
}

export default function InneihCategory({ searchInneih, isLoading }: Props) {
  const form = useAppForm({
    defaultValues: {
      registration_no: '',
      mipa_hming: '',
      mipa_pa_hming: '',
      hmeichhe_hming: '',
      hmeichhe_pa_hming: '',
      inneih_ni: '',
      hmun: '',
      inneihtirtu: '',
    },
    onSubmit: async ({ value }) => {
      searchInneih(value)
    },
  })
  return (
    <Card className="my-4 bg-white">
      <CardContent>
        <div className="flex justify-between">
          <Breadcrumb className="mb-4">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Home</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator>
                /
              </BreadcrumbSeparator>
              <BreadcrumbItem>
                <BreadcrumbLink href="/category/5">Inneih Records</BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
          <Button size="icon" variant="ghost">
            <SearchIcon className="text-xl" />
          </Button>
        </div>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="grid grid-cols-4 gap-4"
        >
          <form.AppField
            name="registration_no"
            children={field => <field.InputField label="Registration No" />}
          />
          <form.AppField
            name="mipa_hming"
            children={field => <field.InputField label="Mipa hming" />}
          />
          <form.AppField
            name="mipa_pa_hming"
            children={field => <field.InputField label="Mipa Pa hming" />}
          />
          <form.AppField
            name="hmeichhe_hming"
            children={field => <field.InputField label="Hmeichhe hming" />}
          />

          <form.AppField
            name="hmeichhe_pa_hming"
            children={field => <field.InputField label="Hmeichhe nu hming" />}
          />
          <form.AppField
            name="inneih_ni"
            children={field => <field.InputField label="Inneih ni" type="date" />}
          />
          <form.AppField
            name="hmun"
            children={field => <field.InputField label="Hmun" />}
          />
          <form.AppField
            name="inneihtirtu"
            children={field => <field.InputField label="Inneih tir tu" />}
          />
          <div className="col-span-1">
            <Button type="submit" isLoading={isLoading}>
              Search
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
